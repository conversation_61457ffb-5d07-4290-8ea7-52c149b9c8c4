# Camí - Authentic Cycling Tours in Catalonia

A modern, responsive, multilingual website for Camí cycling tours, built with Next.js 14, TypeScript, and Tailwind CSS.

## 🎨 Brand Identity

**Camí** means "path" in Catalan. The brand represents authentic, anti-corporate cycling experiences across Catalonia.

### Color Palette
- **<PERSON> White** (#F6F3EF) - Background
- **Pyrenean Slate** (#474A4F) - Primary text/shapes
- **<PERSON> Green** (#7B8054) - Accent/buttons
- **Sun-Baked Clay** (#B97A56) - Highlights/CTAs
- **Mediterranean Blue** (#4C6A92) - Secondary/links
- **Shadow Black** (#232323) - Overlays/contrast

### Typography
- **Headings**: Space Mono (monospaced, technical precision)
- **Body**: Inter (clean, legible sans-serif)

## 🚀 Features

- **Multilingual Support**: English, Spanish, Russian
- **Responsive Design**: Mobile-first approach
- **Digital Grain Textures**: Authentic visual texture system
- **Geometric Placeholders**: SVG-based placeholder system
- **SEO Optimized**: Meta tags, schema markup, sitemap
- **Accessibility**: ARIA labels, keyboard navigation, high contrast support
- **Performance**: Optimized images, lazy loading, minimal bundle size

## 📁 Project Structure

```
src/
├── app/
│   ├── [locale]/           # Internationalized routes
│   │   ├── layout.tsx      # Root layout
│   │   ├── page.tsx        # Homepage
│   │   ├── tours/          # Tours page
│   │   ├── about/          # About page
│   │   ├── gallery/        # Gallery page
│   │   └── contact/        # Contact page
│   └── globals.css         # Global styles with grain textures
├── components/
│   ├── ui/                 # Reusable UI components
│   ├── layout/             # Header, footer, navigation
│   ├── sections/           # Page-specific sections
│   └── placeholders/       # Geometric placeholder components
├── lib/
│   ├── utils.ts           # Utility functions
│   └── constants.ts       # Brand constants, tours data
├── messages/              # i18n translations (en, es, ru)
└── styles/               # Additional CSS files
```

## 🛠 Tech Stack

- **Framework**: Next.js 14 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **Internationalization**: next-intl
- **Icons**: Lucide React
- **Forms**: React Hook Form with Zod validation
- **Animations**: Framer Motion (ready for implementation)

## 🎯 Key Components

### GeometricPlaceholder
Reusable component for creating branded placeholder images with:
- Customizable dimensions and shapes
- Digital grain texture overlays
- Geometric accent elements
- Brand color variants

### Button
Branded button component with:
- Multiple variants (primary, secondary, accent, outline, ghost)
- Geometric corner accents
- Grain texture overlays
- Accessibility features

### LanguageSwitcher
Multilingual navigation with:
- Flag indicators
- Active state styling
- Smooth transitions

## 🌐 Pages Overview

### Homepage
- Hero section with geometric banner
- Tour overview grid
- About snippet
- Testimonials
- Instagram feed placeholder

### Tours (Our Camins)
- Tour details with stats
- Pricing and booking CTAs
- Digital GPX products section
- Alternating layout design

### About
- Founder story with portrait
- Company philosophy
- Values grid with icons
- Team member profiles

### Gallery
- Masonry grid layout
- Category filtering
- Instagram integration
- Photo submission CTA

### Contact
- Contact form with validation
- Multiple contact methods
- Map placeholder
- Social media links

## 🎨 Design System

### Grid System
- 8px base grid for consistent spacing
- Responsive breakpoints
- Generous white space

### Digital Grain Textures
- Light grain (15% opacity) for backgrounds
- Medium grain (8% opacity) for cards
- Animated grain shift effect

### Geometric Elements
- Corner accents on components
- Decorative shapes as dividers
- Consistent geometric patterns

## 🚀 Getting Started

1. **Install Dependencies**
   ```bash
   npm install
   ```

2. **Run Development Server**
   ```bash
   npm run dev
   ```

3. **Build for Production**
   ```bash
   npm run build
   npm start
   ```

## 📝 Content Management

### Updating Translations
Edit files in `src/messages/`:
- `en.json` - English
- `es.json` - Spanish  
- `ru.json` - Russian

### Updating Tours
Modify `TOURS` array in `src/lib/constants.ts`

### Updating Brand Colors
Update color values in:
- `tailwind.config.js`
- `src/app/globals.css`
- `src/lib/constants.ts`

### Replacing Placeholders
1. Add real images to `public/images/`
2. Replace `GeometricPlaceholder` components with `next/image`
3. Maintain aspect ratios and grain overlays

## 🔧 Customization

### Adding New Pages
1. Create page in `src/app/[locale]/new-page/page.tsx`
2. Add navigation link in `Header.tsx`
3. Add translations in message files

### Modifying Grain Textures
Update CSS classes in `src/app/globals.css`:
- `.grain-light` - Background textures
- `.grain-medium` - Card textures

### Custom Components
Follow the established patterns:
- Use `cn()` utility for className merging
- Include grain texture options
- Add geometric accent elements
- Ensure accessibility features

## 📱 Responsive Design

- **Mobile**: Single column layouts, stacked navigation
- **Tablet**: Two-column grids, collapsible menu
- **Desktop**: Multi-column layouts, full navigation

## ♿ Accessibility

- High contrast color ratios
- Keyboard navigation support
- Screen reader friendly
- Focus indicators
- Alt text for all images
- ARIA labels where needed

## 🌍 SEO Features

- Multilingual meta tags
- Open Graph images
- Twitter Card support
- Structured data markup
- Sitemap generation
- Canonical URLs

## 📄 License

© 2024 Camí Cycling Tours. All rights reserved.

---

**Ready to explore authentic Catalonia?** 🚴‍♂️
