import { useTranslations } from 'next-intl'
import { GeometricPlaceholder } from '@/components/placeholders/GeometricPlaceholder'
import { Button } from '@/components/ui/Button'
import { Heart, Mountain, Users, Award } from 'lucide-react'

export default function AboutPage() {
  const t = useTranslations()

  const values = [
    {
      icon: Heart,
      title: 'Authentic Experiences',
      description: 'We believe in real connections with local culture, not sanitized tourist experiences.'
    },
    {
      icon: Mountain,
      title: 'Local Expertise',
      description: 'Born and raised in Catalonia, we know every hidden trail and secret viewpoint.'
    },
    {
      icon: Users,
      title: 'Small Groups',
      description: 'Maximum 8 riders per tour ensures personalized attention and genuine connections.'
    },
    {
      icon: Award,
      title: 'Quality First',
      description: 'Premium bikes, expert guides, and carefully curated routes for unforgettable adventures.'
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative h-96 flex items-center justify-center grain-light">
        <div className="absolute inset-0">
          <GeometricPlaceholder
            width={1920}
            height={400}
            text="ABOUT BANNER: Catalonian Landscape"
            variant="primary"
            className="w-full h-full"
          />
        </div>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="font-mono text-5xl md:text-6xl font-bold text-stone-white mb-4">
            {t('about.title')}
          </h1>
          <p className="text-xl text-stone-white/90">
            {t('about.subtitle')}
          </p>
        </div>
      </section>

      {/* Founder Story */}
      <section className="py-20 px-4 max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          <div>
            <GeometricPlaceholder
              width={500}
              height={600}
              text="FOUNDER PORTRAIT: Clean High-Contrast Portrait"
              variant="secondary"
              className="w-full"
            />
          </div>
          
          <div className="space-y-6">
            <h2 className="font-mono text-3xl font-bold text-pyrenean-slate">
              Our Story
            </h2>
            <div className="space-y-4 text-lg text-pyrenean-slate/80 leading-relaxed">
              <p>
                Camí was born from a simple frustration: too many cycling tours felt like corporate packages, 
                missing the soul of what makes Catalonia special.
              </p>
              <p>
                Founded by local cyclists who've spent decades exploring every corner of our homeland, 
                we created Camí to share the real Catalonia – the hidden trails, the family-run restaurants, 
                the viewpoints that don't appear in guidebooks.
              </p>
              <p>
                We're not just guides; we're your neighbors, passionate about showing you why we call 
                this place home. Every route is personal, every recommendation comes from the heart.
              </p>
            </div>
            
            <div className="pt-4">
              <Button variant="outline" size="lg">
                Meet Our Team
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Philosophy */}
      <section className="py-20 bg-pyrenean-slate/5 grain-medium">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="font-mono text-3xl font-bold text-pyrenean-slate mb-8">
            Our Philosophy
          </h2>
          <div className="text-xl text-pyrenean-slate/80 leading-relaxed space-y-6">
            <p>
              <strong className="text-pyrenean-slate">Camí</strong> means "path" in Catalan. 
              It's not just about the destination – it's about the journey, the discovery, 
              the moments that stay with you long after you've returned home.
            </p>
            <p>
              We believe cycling is the perfect way to experience a place: fast enough to cover ground, 
              slow enough to truly see. Our tours aren't races; they're explorations.
            </p>
            <p>
              Anti-corporate by design, we keep our groups small, our approach personal, 
              and our focus on what matters: authentic connections with the landscape and culture of Catalonia.
            </p>
          </div>
        </div>
      </section>

      {/* Values Grid */}
      <section className="py-20 px-4 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="font-mono text-3xl font-bold text-pyrenean-slate mb-4">
            What Drives Us
          </h2>
          <p className="text-lg text-pyrenean-slate/80 max-w-2xl mx-auto">
            Four principles that guide everything we do at Camí.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {values.map((value, index) => (
            <div key={value.title} className="text-center group">
              <div className="mb-6">
                <div className="w-16 h-16 mx-auto bg-olive-green/10 rounded-full flex items-center justify-center group-hover:bg-olive-green/20 transition-colors grain-light">
                  <value.icon size={32} className="text-olive-green" />
                </div>
              </div>
              
              <h3 className="font-mono text-xl font-semibold text-pyrenean-slate mb-3">
                {value.title}
              </h3>
              <p className="text-pyrenean-slate/70 leading-relaxed">
                {value.description}
              </p>
            </div>
          ))}
        </div>
      </section>

      {/* Team Section */}
      <section className="py-20 bg-pyrenean-slate/5 grain-light">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-mono text-3xl font-bold text-pyrenean-slate mb-4">
              Meet the Team
            </h2>
            <p className="text-lg text-pyrenean-slate/80 max-w-2xl mx-auto">
              Local cyclists, certified guides, and passionate storytellers.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {['Marc - Founder & Lead Guide', 'Anna - Mountain Specialist', 'Jordi - Coast Expert'].map((member, index) => (
              <div key={member} className="text-center">
                <div className="mb-4">
                  <GeometricPlaceholder
                    width={250}
                    height={250}
                    text={member.split(' - ')[0]}
                    shape="circle"
                    variant={index % 3 === 0 ? 'primary' : index % 3 === 1 ? 'secondary' : 'accent'}
                    className="mx-auto"
                  />
                </div>
                
                <h3 className="font-mono text-lg font-semibold text-pyrenean-slate mb-2">
                  {member.split(' - ')[0]}
                </h3>
                <p className="text-pyrenean-slate/70 mb-3">
                  {member.split(' - ')[1]}
                </p>
                <p className="text-sm text-pyrenean-slate/60">
                  {index === 0 && "15+ years guiding in Catalonia"}
                  {index === 1 && "Pyrenees climbing specialist"}
                  {index === 2 && "Mediterranean route expert"}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 max-w-4xl mx-auto text-center">
        <h2 className="font-mono text-3xl font-bold text-pyrenean-slate mb-6">
          Ready to Discover Your Camí?
        </h2>
        <p className="text-lg text-pyrenean-slate/80 mb-8 leading-relaxed">
          Join us for an authentic cycling adventure through the real Catalonia. 
          Small groups, local expertise, unforgettable experiences.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="primary" size="lg">
            View Our Tours
          </Button>
          <Button variant="outline" size="lg">
            Get in Touch
          </Button>
        </div>
      </section>
    </div>
  )
}
