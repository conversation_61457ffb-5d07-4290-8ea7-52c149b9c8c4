import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/Button'
import { GeometricPlaceholder } from '@/components/placeholders/GeometricPlaceholder'
import { TOURS } from '@/lib/constants'
import { formatPrice, getDifficultyColor } from '@/lib/utils'
import { MapPin, Clock, TrendingUp, Star } from 'lucide-react'

export default function ToursPage() {
  const t = useTranslations()

  return (
    <div className="min-h-screen">
      {/* Header Banner */}
      <section className="relative h-96 flex items-center justify-center grain-light">
        <div className="absolute inset-0">
          <GeometricPlaceholder
            width={1920}
            height={400}
            text="TOURS BANNER: Geometric Landscape"
            variant="secondary"
            className="w-full h-full"
          />
        </div>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="font-mono text-5xl md:text-6xl font-bold text-stone-white mb-4">
            {t('tours.title')}
          </h1>
          <p className="text-xl text-stone-white/90">
            {t('tours.subtitle')}
          </p>
        </div>
      </section>

      {/* Tours Grid */}
      <section className="py-20 px-4 max-w-7xl mx-auto">
        <div className="space-y-16">
          {TOURS.map((tour, index) => (
            <div 
              key={tour.id} 
              className={`grid grid-cols-1 lg:grid-cols-2 gap-12 items-center ${
                index % 2 === 1 ? 'lg:grid-flow-col-dense' : ''
              }`}
            >
              {/* Tour Image */}
              <div className={index % 2 === 1 ? 'lg:col-start-2' : ''}>
                <GeometricPlaceholder
                  width={600}
                  height={400}
                  text={`TOUR: ${tour.name} Landscape`}
                  variant={index % 3 === 0 ? 'primary' : index % 3 === 1 ? 'secondary' : 'accent'}
                  className="w-full"
                />
              </div>

              {/* Tour Details */}
              <div className={`space-y-6 ${index % 2 === 1 ? 'lg:col-start-1 lg:row-start-1' : ''}`}>
                <div>
                  <h2 className="font-mono text-3xl font-bold text-pyrenean-slate mb-3">
                    {t(`tours.${tour.id}.name`)}
                  </h2>
                  <p className="text-lg text-pyrenean-slate/80 leading-relaxed">
                    {t(`tours.${tour.id}.description`)}
                  </p>
                </div>

                {/* Tour Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="flex items-center space-x-2">
                    <Clock size={20} className="text-mediterranean-blue" />
                    <div>
                      <div className="text-sm text-pyrenean-slate/60">Duration</div>
                      <div className="font-mono font-semibold">{tour.duration} days</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <TrendingUp size={20} className={getDifficultyColor(tour.difficulty)} />
                    <div>
                      <div className="text-sm text-pyrenean-slate/60">Difficulty</div>
                      <div className={`font-mono font-semibold ${getDifficultyColor(tour.difficulty)}`}>
                        {tour.difficulty}
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <Star size={20} className="text-sun-baked-clay" />
                    <div>
                      <div className="text-sm text-pyrenean-slate/60">Price</div>
                      <div className="font-mono font-semibold">{formatPrice(tour.price)}</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <MapPin size={20} className="text-olive-green" />
                    <div>
                      <div className="text-sm text-pyrenean-slate/60">Region</div>
                      <div className="font-mono font-semibold">Catalonia</div>
                    </div>
                  </div>
                </div>

                {/* Highlights */}
                <div>
                  <h3 className="font-mono font-semibold text-pyrenean-slate mb-3">Highlights</h3>
                  <div className="flex flex-wrap gap-2">
                    {tour.highlights.map((highlight, highlightIndex) => (
                      <span
                        key={highlightIndex}
                        className="px-3 py-1 bg-pyrenean-slate/10 text-pyrenean-slate text-sm font-medium grain-medium"
                      >
                        {highlight}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col sm:flex-row gap-4">
                  <Button variant="primary" size="lg" className="flex-1">
                    {t('tours.book')} - {formatPrice(tour.price)}
                  </Button>
                  <Button variant="outline" size="lg" className="flex-1">
                    {t('tours.inquire')}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Digital Products Section */}
      <section className="py-20 bg-pyrenean-slate/5 grain-medium">
        <div className="max-w-6xl mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="font-mono text-3xl font-bold text-pyrenean-slate mb-4">
              Digital Route Packs
            </h2>
            <p className="text-lg text-pyrenean-slate/80 max-w-2xl mx-auto">
              Can't join a guided tour? Download our carefully crafted GPX routes and explore Catalonia at your own pace.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {['Complete Routes Pack', 'Mountain Routes', 'Coastal Routes'].map((pack, index) => (
              <div key={pack} className="bg-stone-white border border-pyrenean-slate/10 p-6 grain-light">
                <div className="mb-4">
                  <GeometricPlaceholder
                    width={300}
                    height={200}
                    text={`GPX PACK: ${pack}`}
                    variant={index % 3 === 0 ? 'primary' : index % 3 === 1 ? 'secondary' : 'accent'}
                    className="w-full"
                  />
                </div>
                
                <h3 className="font-mono text-xl font-semibold text-pyrenean-slate mb-2">
                  {pack}
                </h3>
                <p className="text-pyrenean-slate/70 mb-4">
                  Professional GPX files with detailed route notes and local recommendations.
                </p>
                
                <div className="flex justify-between items-center">
                  <span className="font-mono text-lg font-bold text-pyrenean-slate">
                    {formatPrice(29 + index * 10)}
                  </span>
                  <Button variant="accent" size="sm">
                    Download
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}
