'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { GeometricPlaceholder } from '@/components/placeholders/GeometricPlaceholder'
import { Button } from '@/components/ui/Button'
import { GALLERY_CATEGORIES } from '@/lib/constants'

export default function GalleryPage() {
  const t = useTranslations()
  const [activeFilter, setActiveFilter] = useState('all')

  // Generate gallery items with different categories
  const galleryItems = Array.from({ length: 20 }, (_, index) => ({
    id: index + 1,
    category: GALLERY_CATEGORIES[Math.floor(Math.random() * GALLERY_CATEGORIES.length)],
    title: `Gallery Image ${index + 1}`,
    description: 'Real cycling moment from our tours'
  }))

  const filteredItems = activeFilter === 'all' 
    ? galleryItems 
    : galleryItems.filter(item => item.category === activeFilter)

  return (
    <div className="min-h-screen">
      {/* Header */}
      <section className="relative h-96 flex items-center justify-center grain-light">
        <div className="absolute inset-0">
          <GeometricPlaceholder
            width={1920}
            height={400}
            text="GALLERY BANNER: Cycling Adventures Collage"
            variant="accent"
            className="w-full h-full"
          />
        </div>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="font-mono text-5xl md:text-6xl font-bold text-stone-white mb-4">
            {t('gallery.title')}
          </h1>
          <p className="text-xl text-stone-white/90">
            {t('gallery.subtitle')}
          </p>
        </div>
      </section>

      {/* Filter Buttons */}
      <section className="py-8 px-4 max-w-7xl mx-auto">
        <div className="flex flex-wrap justify-center gap-4">
          {GALLERY_CATEGORIES.map((category) => (
            <Button
              key={category}
              variant={activeFilter === category ? 'primary' : 'outline'}
              size="sm"
              onClick={() => setActiveFilter(category)}
              className="capitalize"
            >
              {t(`gallery.filters.${category}`)}
            </Button>
          ))}
        </div>
      </section>

      {/* Masonry Gallery Grid */}
      <section className="px-4 max-w-7xl mx-auto pb-20">
        <div className="columns-1 sm:columns-2 lg:columns-3 xl:columns-4 gap-4 space-y-4">
          {filteredItems.map((item, index) => {
            // Vary heights for masonry effect
            const heights = [250, 300, 350, 400, 200, 450]
            const height = heights[index % heights.length]
            
            return (
              <div 
                key={item.id} 
                className="break-inside-avoid mb-4 group cursor-pointer"
              >
                <div className="relative overflow-hidden grain-medium hover:grain-light transition-all duration-300">
                  <GeometricPlaceholder
                    width={300}
                    height={height}
                    text={`${item.category.toUpperCase()}: Real Cycling Image`}
                    variant={
                      item.category === 'mountain' ? 'primary' :
                      item.category === 'coast' ? 'secondary' :
                      item.category === 'urban' ? 'accent' : 'primary'
                    }
                    className="w-full group-hover:scale-105 transition-transform duration-300"
                  />
                  
                  {/* Overlay on hover */}
                  <div className="absolute inset-0 bg-shadow-black/0 group-hover:bg-shadow-black/20 transition-all duration-300 flex items-end">
                    <div className="p-4 text-stone-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                      <h3 className="font-mono font-semibold mb-1">
                        {item.title}
                      </h3>
                      <p className="text-sm text-stone-white/80">
                        {item.description}
                      </p>
                      <div className="mt-2">
                        <span className="inline-block px-2 py-1 bg-olive-green/80 text-xs font-mono uppercase tracking-wide">
                          {item.category}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Load More Button */}
        <div className="text-center mt-12">
          <Button variant="outline" size="lg">
            Load More Images
          </Button>
        </div>
      </section>

      {/* Instagram CTA */}
      <section className="py-20 bg-pyrenean-slate/5 grain-medium">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="font-mono text-3xl font-bold text-pyrenean-slate mb-6">
            Follow Our Daily Adventures
          </h2>
          <p className="text-lg text-pyrenean-slate/80 mb-8 leading-relaxed">
            Get real-time updates from our tours, behind-the-scenes moments, 
            and daily inspiration from the trails of Catalonia.
          </p>
          
          <div className="grid grid-cols-3 md:grid-cols-6 gap-2 mb-8">
            {Array.from({ length: 6 }).map((_, index) => (
              <GeometricPlaceholder
                key={index}
                width={150}
                height={150}
                text={`IG ${index + 1}`}
                shape="square"
                variant={index % 3 === 0 ? 'primary' : index % 3 === 1 ? 'secondary' : 'accent'}
                className="w-full hover:scale-105 transition-transform duration-300 cursor-pointer"
              />
            ))}
          </div>
          
          <Button variant="secondary" size="lg">
            @cami.cycling on Instagram
          </Button>
        </div>
      </section>

      {/* Photo Submission CTA */}
      <section className="py-20 px-4 max-w-4xl mx-auto text-center">
        <h2 className="font-mono text-3xl font-bold text-pyrenean-slate mb-6">
          Share Your Camí Moments
        </h2>
        <p className="text-lg text-pyrenean-slate/80 mb-8 leading-relaxed">
          Been on one of our tours? We'd love to feature your photos! 
          Tag us @cami.cycling or use #MyCami to be featured in our gallery.
        </p>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button variant="primary" size="lg">
            Submit Your Photos
          </Button>
          <Button variant="outline" size="lg">
            Tour Guidelines
          </Button>
        </div>
      </section>
    </div>
  )
}
