import { useTranslations } from 'next-intl'
import { Button } from '@/components/ui/Button'
import { GeometricPlaceholder } from '@/components/placeholders/GeometricPlaceholder'
import { TOURS, TESTIMONIALS } from '@/lib/constants'
import Link from 'next/link'

export default function HomePage() {
  const t = useTranslations()

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center justify-center grain-light">
        <div className="absolute inset-0">
          <GeometricPlaceholder
            width={1920}
            height={1080}
            text="HERO: Geometric Cycling Banner"
            variant="primary"
            className="w-full h-full"
          />
        </div>
        
        {/* Hero Content */}
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="font-mono text-6xl md:text-8xl font-bold text-stone-white mb-4 animate-fade-in">
            {t('hero.title')}
          </h1>
          <p className="text-xl md:text-2xl text-stone-white/90 mb-8 animate-slide-up">
            {t('hero.subtitle')}
          </p>
          <Button 
            variant="accent" 
            size="lg"
            className="animate-slide-up"
          >
            {t('hero.cta')}
          </Button>
        </div>

        {/* Geometric decorative elements */}
        <div className="absolute top-20 left-20 w-4 h-4 bg-sun-baked-clay opacity-60 rotate-45 animate-pulse" />
        <div className="absolute bottom-32 right-32 w-6 h-6 bg-mediterranean-blue opacity-40 rounded-full animate-pulse" />
        <div className="absolute top-1/2 left-10 w-2 h-16 bg-olive-green opacity-50" />
      </section>

      {/* Tours Overview Grid */}
      <section className="py-20 px-4 max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="font-mono text-4xl font-bold text-pyrenean-slate mb-4">
            {t('tours.title')}
          </h2>
          <p className="text-lg text-pyrenean-slate/80 max-w-2xl mx-auto">
            {t('tours.subtitle')}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {TOURS.map((tour, index) => (
            <div key={tour.id} className="group">
              <div className="mb-4">
                <GeometricPlaceholder
                  width={300}
                  height={200}
                  text={`TOUR: ${tour.name}`}
                  variant={index % 2 === 0 ? 'primary' : 'secondary'}
                  className="w-full group-hover:scale-105 transition-transform duration-300"
                />
              </div>
              
              <div className="space-y-3">
                <h3 className="font-mono text-xl font-semibold text-pyrenean-slate">
                  {t(`tours.${tour.id}.name`)}
                </h3>
                <p className="text-pyrenean-slate/70">
                  {t(`tours.${tour.id}.description`)}
                </p>
                
                <div className="flex justify-between items-center text-sm">
                  <span className="text-pyrenean-slate/60">
                    {t(`tours.${tour.id}.duration`)}
                  </span>
                  <span className="text-pyrenean-slate/60">
                    {t(`tours.${tour.id}.difficulty`)}
                  </span>
                </div>
                
                <div className="flex space-x-2">
                  <Button variant="primary" size="sm" className="flex-1">
                    {t('tours.book')}
                  </Button>
                  <Button variant="outline" size="sm" className="flex-1">
                    {t('tours.inquire')}
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* About Snippet */}
      <section className="py-20 bg-pyrenean-slate/5 grain-medium">
        <div className="max-w-4xl mx-auto px-4 text-center">
          <h2 className="font-mono text-3xl font-bold text-pyrenean-slate mb-6">
            {t('about.title')}
          </h2>
          <p className="text-lg text-pyrenean-slate/80 mb-8 leading-relaxed">
            {t('about.philosophy')}
          </p>
          <Link href="/about">
            <Button variant="outline">
              Learn More About Us
            </Button>
          </Link>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 px-4 max-w-7xl mx-auto">
        <h2 className="font-mono text-3xl font-bold text-pyrenean-slate text-center mb-16">
          What Our Riders Say
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {TESTIMONIALS.map((testimonial) => (
            <div key={testimonial.id} className="bg-stone-white border border-pyrenean-slate/10 p-6 grain-medium">
              <div className="mb-4">
                <GeometricPlaceholder
                  width={60}
                  height={60}
                  text={testimonial.name.charAt(0)}
                  shape="circle"
                  variant="accent"
                  className="mx-auto"
                />
              </div>
              
              <blockquote className="text-pyrenean-slate/80 mb-4 text-sm leading-relaxed">
                "{testimonial.text}"
              </blockquote>
              
              <div className="text-center">
                <div className="font-mono font-semibold text-pyrenean-slate text-sm">
                  {testimonial.name}
                </div>
                <div className="text-pyrenean-slate/60 text-xs">
                  {testimonial.location}
                </div>
              </div>
            </div>
          ))}
        </div>
      </section>

      {/* Instagram Feed Placeholder */}
      <section className="py-20 bg-pyrenean-slate/5 grain-light">
        <div className="max-w-6xl mx-auto px-4">
          <h2 className="font-mono text-3xl font-bold text-pyrenean-slate text-center mb-16">
            Follow Our Adventures
          </h2>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <GeometricPlaceholder
                key={index}
                width={200}
                height={200}
                text={`INSTAGRAM ${index + 1}`}
                shape="square"
                variant={index % 3 === 0 ? 'primary' : index % 3 === 1 ? 'secondary' : 'accent'}
                className="w-full hover:scale-105 transition-transform duration-300 cursor-pointer"
              />
            ))}
          </div>
          
          <div className="text-center mt-8">
            <Button variant="secondary">
              View on Instagram
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
