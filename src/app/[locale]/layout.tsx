import { NextIntlClientProvider } from 'next-intl'
import { getMessages } from 'next-intl/server'
import { Inter, Space_Mono } from 'next/font/google'
import { Header } from '@/components/layout/Header'
import { Footer } from '@/components/layout/Footer'
import '../globals.css'

const inter = Inter({ 
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap'
})

const spaceMono = Space_Mono({ 
  weight: ['400', '700'],
  subsets: ['latin'],
  variable: '--font-space-mono',
  display: 'swap'
})

export async function generateMetadata({ params: { locale } }: { params: { locale: string } }) {
  const messages = await getMessages()
  const meta = (messages as any).meta || {}
  
  return {
    title: meta.title || 'Camí – Authentic Cycling Tours in Catalonia',
    description: meta.description || 'Clean, modern, and authentic cycling tours and digital routes across Catalonia. Book your path with local experts.',
    keywords: 'cycling tours, Catalonia, Spain, bike tours, gravel cycling, mountain biking, coastal cycling, authentic travel',
    authors: [{ name: 'Camí Cycling Tours' }],
    creator: 'Camí Cycling Tours',
    publisher: 'Camí Cycling Tours',
    formatDetection: {
      email: false,
      address: false,
      telephone: false,
    },
    metadataBase: new URL('https://cami.cat'),
    alternates: {
      canonical: '/',
      languages: {
        'en': '/en',
        'es': '/es',
        'ru': '/ru',
      },
    },
    openGraph: {
      title: meta.title || 'Camí – Authentic Cycling Tours in Catalonia',
      description: meta.description || 'Clean, modern, and authentic cycling tours and digital routes across Catalonia.',
      url: 'https://cami.cat',
      siteName: 'Camí Cycling Tours',
      images: [
        {
          url: '/images/og-image.jpg',
          width: 1200,
          height: 630,
          alt: 'Camí Cycling Tours in Catalonia',
        },
      ],
      locale: locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: meta.title || 'Camí – Authentic Cycling Tours in Catalonia',
      description: meta.description || 'Clean, modern, and authentic cycling tours and digital routes across Catalonia.',
      images: ['/images/og-image.jpg'],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  }
}

export default async function LocaleLayout({
  children,
  params: { locale }
}: {
  children: React.ReactNode
  params: { locale: string }
}) {
  const messages = await getMessages()

  return (
    <html lang={locale} className={`${inter.variable} ${spaceMono.variable}`}>
      <body className="font-sans bg-stone-white text-pyrenean-slate antialiased">
        <NextIntlClientProvider messages={messages}>
          <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-1">
              {children}
            </main>
            <Footer />
          </div>
        </NextIntlClientProvider>
      </body>
    </html>
  )
}
