'use client'

import { useState } from 'react'
import { useTranslations } from 'next-intl'
import { useForm } from 'react-hook-form'
import { GeometricPlaceholder } from '@/components/placeholders/GeometricPlaceholder'
import { Button } from '@/components/ui/Button'
import { CONTACT_INFO, SOCIAL_LINKS, TOURS } from '@/lib/constants'
import { Mail, Phone, MapPin, MessageCircle, Send, Instagram } from 'lucide-react'

interface ContactFormData {
  name: string
  email: string
  tour: string
  dates: string
  message: string
}

export default function ContactPage() {
  const t = useTranslations()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)

  const { register, handleSubmit, formState: { errors }, reset } = useForm<ContactFormData>()

  const onSubmit = async (data: ContactFormData) => {
    setIsSubmitting(true)
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000))
    console.log('Form submitted:', data)
    setIsSubmitted(true)
    setIsSubmitting(false)
    reset()
  }

  const contactMethods = [
    {
      icon: Mail,
      label: 'Email',
      value: CONTACT_INFO.email,
      href: `mailto:${CONTACT_INFO.email}`,
      description: 'Best for detailed inquiries'
    },
    {
      icon: MessageCircle,
      label: 'WhatsApp',
      value: '+34 600 000 000',
      href: SOCIAL_LINKS.whatsapp,
      description: 'Quick questions & booking'
    },
    {
      icon: Send,
      label: 'Telegram',
      value: '@camicycling',
      href: SOCIAL_LINKS.telegram,
      description: 'Instant messaging'
    },
    {
      icon: Phone,
      label: 'Phone',
      value: CONTACT_INFO.phone,
      href: `tel:${CONTACT_INFO.phone}`,
      description: 'Direct conversation'
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Header */}
      <section className="relative h-96 flex items-center justify-center grain-light">
        <div className="absolute inset-0">
          <GeometricPlaceholder
            width={1920}
            height={400}
            text="CONTACT BANNER: Catalonia Map with Geometric Overlay"
            variant="primary"
            className="w-full h-full"
          />
        </div>
        
        <div className="relative z-10 text-center max-w-4xl mx-auto px-4">
          <h1 className="font-mono text-5xl md:text-6xl font-bold text-stone-white mb-4">
            {t('contact.title')}
          </h1>
          <p className="text-xl text-stone-white/90">
            {t('contact.subtitle')}
          </p>
        </div>
      </section>

      <div className="max-w-7xl mx-auto px-4 py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16">
          {/* Contact Form */}
          <div>
            <h2 className="font-mono text-2xl font-bold text-pyrenean-slate mb-8">
              Send us a message
            </h2>

            {isSubmitted ? (
              <div className="bg-olive-green/10 border border-olive-green/20 p-6 grain-light text-center">
                <h3 className="font-mono text-lg font-semibold text-olive-green mb-2">
                  Message Sent!
                </h3>
                <p className="text-pyrenean-slate/80">
                  Thanks for reaching out. We'll get back to you within 24 hours.
                </p>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="mt-4"
                  onClick={() => setIsSubmitted(false)}
                >
                  Send Another Message
                </Button>
              </div>
            ) : (
              <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block font-mono font-medium text-pyrenean-slate mb-2">
                      {t('contact.form.name')} *
                    </label>
                    <input
                      {...register('name', { required: 'Name is required' })}
                      className="w-full px-4 py-3 border border-pyrenean-slate/20 bg-stone-white focus:outline-none focus:ring-2 focus:ring-olive-green/50 grain-medium"
                      placeholder="Your name"
                    />
                    {errors.name && (
                      <p className="text-sun-baked-clay text-sm mt-1">{errors.name.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block font-mono font-medium text-pyrenean-slate mb-2">
                      {t('contact.form.email')} *
                    </label>
                    <input
                      {...register('email', { 
                        required: 'Email is required',
                        pattern: {
                          value: /^\S+@\S+$/i,
                          message: 'Invalid email address'
                        }
                      })}
                      type="email"
                      className="w-full px-4 py-3 border border-pyrenean-slate/20 bg-stone-white focus:outline-none focus:ring-2 focus:ring-olive-green/50 grain-medium"
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <p className="text-sun-baked-clay text-sm mt-1">{errors.email.message}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block font-mono font-medium text-pyrenean-slate mb-2">
                      {t('contact.form.tour')}
                    </label>
                    <select
                      {...register('tour')}
                      className="w-full px-4 py-3 border border-pyrenean-slate/20 bg-stone-white focus:outline-none focus:ring-2 focus:ring-olive-green/50 grain-medium"
                    >
                      <option value="">Select a tour</option>
                      {TOURS.map((tour) => (
                        <option key={tour.id} value={tour.id}>
                          {tour.name}
                        </option>
                      ))}
                    </select>
                  </div>

                  <div>
                    <label className="block font-mono font-medium text-pyrenean-slate mb-2">
                      {t('contact.form.dates')}
                    </label>
                    <input
                      {...register('dates')}
                      type="text"
                      className="w-full px-4 py-3 border border-pyrenean-slate/20 bg-stone-white focus:outline-none focus:ring-2 focus:ring-olive-green/50 grain-medium"
                      placeholder="e.g., June 2024"
                    />
                  </div>
                </div>

                <div>
                  <label className="block font-mono font-medium text-pyrenean-slate mb-2">
                    {t('contact.form.message')} *
                  </label>
                  <textarea
                    {...register('message', { required: 'Message is required' })}
                    rows={6}
                    className="w-full px-4 py-3 border border-pyrenean-slate/20 bg-stone-white focus:outline-none focus:ring-2 focus:ring-olive-green/50 grain-medium resize-none"
                    placeholder="Tell us about your cycling experience, group size, special requirements..."
                  />
                  {errors.message && (
                    <p className="text-sun-baked-clay text-sm mt-1">{errors.message.message}</p>
                  )}
                </div>

                <Button 
                  type="submit" 
                  variant="primary" 
                  size="lg" 
                  className="w-full"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? 'Sending...' : t('contact.form.submit')}
                </Button>
              </form>
            )}
          </div>

          {/* Contact Information */}
          <div className="space-y-8">
            <div>
              <h2 className="font-mono text-2xl font-bold text-pyrenean-slate mb-8">
                Get in touch directly
              </h2>

              <div className="space-y-6">
                {contactMethods.map((method) => (
                  <a
                    key={method.label}
                    href={method.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-start space-x-4 p-4 border border-pyrenean-slate/10 hover:border-olive-green/30 transition-colors grain-medium group"
                  >
                    <div className="w-12 h-12 bg-olive-green/10 rounded-full flex items-center justify-center group-hover:bg-olive-green/20 transition-colors">
                      <method.icon size={24} className="text-olive-green" />
                    </div>
                    <div>
                      <h3 className="font-mono font-semibold text-pyrenean-slate mb-1">
                        {method.label}
                      </h3>
                      <p className="text-pyrenean-slate font-medium mb-1">
                        {method.value}
                      </p>
                      <p className="text-pyrenean-slate/60 text-sm">
                        {method.description}
                      </p>
                    </div>
                  </a>
                ))}
              </div>
            </div>

            {/* Map Placeholder */}
            <div>
              <h3 className="font-mono text-lg font-semibold text-pyrenean-slate mb-4">
                Find us in Catalonia
              </h3>
              <GeometricPlaceholder
                width={500}
                height={300}
                text="MAP: Minimalist Catalonia with Girona Marker"
                variant="secondary"
                className="w-full"
              />
              <div className="mt-4 flex items-center space-x-2 text-pyrenean-slate/70">
                <MapPin size={16} />
                <span className="text-sm">{CONTACT_INFO.address}</span>
              </div>
            </div>

            {/* Social Links */}
            <div>
              <h3 className="font-mono text-lg font-semibold text-pyrenean-slate mb-4">
                Follow our adventures
              </h3>
              <a
                href={SOCIAL_LINKS.instagram}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-3 text-pyrenean-slate hover:text-olive-green transition-colors"
              >
                <Instagram size={24} />
                <span className="font-medium">@cami.cycling</span>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
