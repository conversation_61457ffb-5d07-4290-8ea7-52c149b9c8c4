@import url('https://fonts.googleapis.com/css2?family=Space+Mono:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --stone-white: #F6F3EF;
  --pyrenean-slate: #474A4F;
  --olive-green: #7B8054;
  --sun-baked-clay: #B97A56;
  --mediterranean-blue: #4C6A92;
  --shadow-black: #232323;
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', sans-serif;
  background-color: var(--stone-white);
  color: var(--pyrenean-slate);
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

a {
  color: inherit;
  text-decoration: none;
}

/* Digital Grain Texture Classes */
.grain-light {
  position: relative;
}

.grain-light::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, transparent 2px, rgba(71, 74, 79, 0.1) 2px),
    radial-gradient(circle at 75% 75%, transparent 2px, rgba(71, 74, 79, 0.1) 2px),
    radial-gradient(circle at 25% 75%, transparent 1px, rgba(71, 74, 79, 0.05) 1px),
    radial-gradient(circle at 75% 25%, transparent 1px, rgba(71, 74, 79, 0.05) 1px);
  background-size: 8px 8px, 8px 8px, 4px 4px, 4px 4px;
  background-position: 0 0, 4px 4px, 2px 2px, 6px 6px;
  pointer-events: none;
  animation: grainShift 20s infinite linear;
  opacity: 0.15;
}

.grain-medium {
  position: relative;
}

.grain-medium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: 
    radial-gradient(circle at 25% 25%, transparent 1px, rgba(71, 74, 79, 0.08) 1px),
    radial-gradient(circle at 75% 75%, transparent 1px, rgba(71, 74, 79, 0.08) 1px);
  background-size: 6px 6px, 6px 6px;
  background-position: 0 0, 3px 3px;
  pointer-events: none;
  animation: grainShift 15s infinite linear;
  opacity: 0.08;
}

/* Geometric shapes utility classes */
.geometric-circle {
  border-radius: 50%;
  background: linear-gradient(135deg, var(--olive-green), var(--mediterranean-blue));
}

.geometric-triangle {
  width: 0;
  height: 0;
  border-style: solid;
}

.geometric-line {
  height: 2px;
  background: linear-gradient(90deg, var(--olive-green), transparent);
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--stone-white);
}

::-webkit-scrollbar-thumb {
  background: var(--pyrenean-slate);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--olive-green);
}

/* Focus styles for accessibility */
*:focus {
  outline: 2px solid var(--mediterranean-blue);
  outline-offset: 2px;
}

/* Button focus styles */
button:focus,
a:focus {
  outline: 2px solid var(--mediterranean-blue);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .grain-light::before,
  .grain-medium::before {
    display: none;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .grain-light::before,
  .grain-medium::before {
    animation: none;
  }
  
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
