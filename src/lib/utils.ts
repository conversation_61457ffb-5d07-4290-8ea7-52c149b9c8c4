import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: number, currency: string = 'EUR'): string {
  return new Intl.NumberFormat('en-EU', {
    style: 'currency',
    currency: currency,
  }).format(price)
}

export function formatDuration(days: number): string {
  return `${days} ${days === 1 ? 'day' : 'days'}`
}

export function getDifficultyColor(difficulty: string): string {
  switch (difficulty.toLowerCase()) {
    case 'easy':
      return 'text-olive-green'
    case 'moderate':
      return 'text-mediterranean-blue'
    case 'challenging':
      return 'text-sun-baked-clay'
    default:
      return 'text-pyrenean-slate'
  }
}

export function generatePlaceholderSVG(
  width: number,
  height: number,
  text: string,
  bgColor: string = '#7B8054',
  textColor: string = '#F6F3EF'
): string {
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <pattern id="grain" patternUnits="userSpaceOnUse" width="4" height="4">
          <circle cx="1" cy="1" r="0.5" fill="${textColor}" opacity="0.1"/>
          <circle cx="3" cy="3" r="0.5" fill="${textColor}" opacity="0.1"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="${bgColor}"/>
      <rect width="100%" height="100%" fill="url(#grain)"/>
      <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
            fill="${textColor}" font-family="Space Mono, monospace" 
            font-size="${Math.min(width, height) / 10}px" opacity="0.8">
        ${text}
      </text>
    </svg>
  `
  return `data:image/svg+xml;base64,${btoa(svg)}`
}

export function getGeometricShape(type: 'circle' | 'triangle' | 'square', size: number = 40): string {
  const color = '#7B8054'
  
  switch (type) {
    case 'circle':
      return `
        <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
          <circle cx="${size/2}" cy="${size/2}" r="${size/2 - 2}" fill="${color}" opacity="0.8"/>
        </svg>
      `
    case 'triangle':
      return `
        <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
          <polygon points="${size/2},2 ${size-2},${size-2} 2,${size-2}" fill="${color}" opacity="0.8"/>
        </svg>
      `
    case 'square':
      return `
        <svg width="${size}" height="${size}" xmlns="http://www.w3.org/2000/svg">
          <rect x="2" y="2" width="${size-4}" height="${size-4}" fill="${color}" opacity="0.8"/>
        </svg>
      `
    default:
      return ''
  }
}
