'use client'

import { useLocale } from 'next-intl'
import { useRouter, usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'

const languages = [
  { code: 'en', name: 'E<PERSON>', flag: '🇬🇧' },
  { code: 'es', name: 'E<PERSON>', flag: '🇪🇸' },
  { code: 'ru', name: 'RU', flag: '🇷🇺' }
]

interface LanguageSwitcherProps {
  className?: string
}

export function LanguageSwitcher({ className }: LanguageSwitcherProps) {
  const locale = useLocale()
  const router = useRouter()
  const pathname = usePathname()

  const handleLanguageChange = (newLocale: string) => {
    // Remove the current locale from the pathname
    const pathWithoutLocale = pathname.replace(`/${locale}`, '') || '/'
    // Navigate to the new locale
    router.push(`/${newLocale}${pathWithoutLocale}`)
  }

  return (
    <div className={cn('flex items-center space-x-1', className)}>
      {languages.map((language) => (
        <button
          key={language.code}
          onClick={() => handleLanguageChange(language.code)}
          className={cn(
            'px-2 py-1 text-sm font-mono font-medium transition-all duration-200',
            'border border-transparent hover:border-pyrenean-slate/30',
            'focus:outline-none focus:ring-2 focus:ring-mediterranean-blue/50',
            'relative overflow-hidden',
            locale === language.code
              ? 'bg-pyrenean-slate text-stone-white'
              : 'text-pyrenean-slate hover:bg-pyrenean-slate/10'
          )}
          aria-label={`Switch to ${language.name}`}
        >
          {/* Geometric accent */}
          <div className="absolute top-0 left-0 w-1 h-1 bg-current opacity-30" />
          
          <span className="flex items-center space-x-1">
            <span className="text-xs">{language.flag}</span>
            <span>{language.name}</span>
          </span>
          
          {/* Active indicator */}
          {locale === language.code && (
            <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-olive-green" />
          )}
        </button>
      ))}
    </div>
  )
}
