'use client'

import { ButtonHTMLAttributes, forwardRef } from 'react'
import { cn } from '@/lib/utils'

interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'accent' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg'
  withGrain?: boolean
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant = 'primary', size = 'md', withGrain = true, children, ...props }, ref) => {
    const baseClasses = 'inline-flex items-center justify-center font-mono font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden'
    
    const variantClasses = {
      primary: 'bg-olive-green text-stone-white hover:bg-olive-green/90 focus:ring-olive-green/50 border-2 border-olive-green',
      secondary: 'bg-mediterranean-blue text-stone-white hover:bg-mediterranean-blue/90 focus:ring-mediterranean-blue/50 border-2 border-mediterranean-blue',
      accent: 'bg-sun-baked-clay text-stone-white hover:bg-sun-baked-clay/90 focus:ring-sun-baked-clay/50 border-2 border-sun-baked-clay',
      outline: 'bg-transparent text-pyrenean-slate border-2 border-pyrenean-slate hover:bg-pyrenean-slate hover:text-stone-white focus:ring-pyrenean-slate/50',
      ghost: 'bg-transparent text-pyrenean-slate hover:bg-pyrenean-slate/10 focus:ring-pyrenean-slate/50 border-2 border-transparent'
    }
    
    const sizeClasses = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg'
    }

    return (
      <button
        className={cn(
          baseClasses,
          variantClasses[variant],
          sizeClasses[size],
          withGrain && 'grain-medium',
          className
        )}
        ref={ref}
        {...props}
      >
        {/* Geometric corner accents */}
        <div className="absolute top-1 left-1 w-1 h-1 bg-current opacity-30 rotate-45" />
        <div className="absolute bottom-1 right-1 w-1 h-1 bg-current opacity-30" />
        
        {/* Button content */}
        <span className="relative z-10 tracking-wide">
          {children}
        </span>
        
        {/* Hover effect overlay */}
        <div className="absolute inset-0 bg-current opacity-0 hover:opacity-10 transition-opacity duration-200" />
      </button>
    )
  }
)

Button.displayName = 'Button'

export { Button }
