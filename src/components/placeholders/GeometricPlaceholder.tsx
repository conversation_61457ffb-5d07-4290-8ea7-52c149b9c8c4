'use client'

import { cn } from '@/lib/utils'
import { BRAND_COLORS } from '@/lib/constants'

interface GeometricPlaceholderProps {
  width?: number
  height?: number
  text: string
  className?: string
  shape?: 'rectangle' | 'square' | 'circle'
  variant?: 'primary' | 'secondary' | 'accent'
  withGrain?: boolean
}

export function GeometricPlaceholder({
  width = 400,
  height = 300,
  text,
  className,
  shape = 'rectangle',
  variant = 'primary',
  withGrain = true
}: GeometricPlaceholderProps) {
  const getVariantColors = () => {
    switch (variant) {
      case 'primary':
        return { bg: BRAND_COLORS.oliveGreen, text: BRAND_COLORS.stoneWhite }
      case 'secondary':
        return { bg: BRAND_COLORS.mediterraneanBlue, text: BRAND_COLORS.stoneWhite }
      case 'accent':
        return { bg: BRAND_COLORS.sunBakedClay, text: BRAND_COLORS.stoneWhite }
      default:
        return { bg: BRAND_COLORS.olive<PERSON>reen, text: BRAND_COLORS.stoneWhite }
    }
  }

  const colors = getVariantColors()
  const aspectRatio = width / height

  const getShapeClasses = () => {
    switch (shape) {
      case 'square':
        return 'aspect-square'
      case 'circle':
        return 'aspect-square rounded-full'
      default:
        return ''
    }
  }

  return (
    <div
      className={cn(
        'relative flex items-center justify-center overflow-hidden',
        'border-2 border-pyrenean-slate/20',
        getShapeClasses(),
        withGrain && 'grain-medium',
        className
      )}
      style={{
        backgroundColor: colors.bg,
        width: shape === 'square' ? Math.min(width, height) : width,
        height: shape === 'square' ? Math.min(width, height) : height,
        aspectRatio: shape === 'rectangle' ? aspectRatio : 1
      }}
    >
      {/* Geometric overlay patterns */}
      <div className="absolute inset-0 opacity-20">
        <svg width="100%" height="100%" className="absolute inset-0">
          <defs>
            <pattern id={`geometric-${variant}`} x="0" y="0" width="40" height="40" patternUnits="userSpaceOnUse">
              <circle cx="10" cy="10" r="1" fill={colors.text} opacity="0.3" />
              <circle cx="30" cy="30" r="1" fill={colors.text} opacity="0.3" />
              <line x1="0" y1="20" x2="40" y2="20" stroke={colors.text} strokeWidth="0.5" opacity="0.2" />
              <line x1="20" y1="0" x2="20" y2="40" stroke={colors.text} strokeWidth="0.5" opacity="0.2" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill={`url(#geometric-${variant})`} />
        </svg>
      </div>

      {/* Corner geometric elements */}
      <div className="absolute top-2 left-2 w-3 h-3 bg-current opacity-60 rotate-45" />
      <div className="absolute bottom-2 right-2 w-2 h-2 rounded-full bg-current opacity-40" />

      {/* Text content */}
      <div className="relative z-10 text-center px-4">
        <div 
          className="font-mono font-bold tracking-wider opacity-80"
          style={{ 
            color: colors.text,
            fontSize: `${Math.max(12, Math.min(width, height) / 15)}px`
          }}
        >
          {text.toUpperCase()}
        </div>
        <div 
          className="font-mono opacity-60 mt-1"
          style={{ 
            color: colors.text,
            fontSize: `${Math.max(8, Math.min(width, height) / 25)}px`
          }}
        >
          PLACEHOLDER
        </div>
      </div>

      {/* Animated grain effect */}
      {withGrain && (
        <div className="absolute inset-0 opacity-10 animate-grain-shift">
          <svg width="100%" height="100%">
            <filter id="noise">
              <feTurbulence type="fractalNoise" baseFrequency="0.9" numOctaves="4" stitchTiles="stitch"/>
            </filter>
            <rect width="100%" height="100%" filter="url(#noise)" opacity="0.1"/>
          </svg>
        </div>
      )}
    </div>
  )
}
