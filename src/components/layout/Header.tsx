'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import { Menu, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import { LanguageSwitcher } from '@/components/ui/LanguageSwitcher'

export function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const t = useTranslations('navigation')
  const locale = useLocale()

  const navigation = [
    { name: t('home'), href: `/${locale}` },
    { name: t('tours'), href: `/${locale}/tours` },
    { name: t('about'), href: `/${locale}/about` },
    { name: t('gallery'), href: `/${locale}/gallery` },
    { name: t('contact'), href: `/${locale}/contact` },
  ]

  return (
    <header className="sticky top-0 z-50 bg-stone-white/95 backdrop-blur-sm border-b border-pyrenean-slate/10 grain-light">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link 
            href={`/${locale}`}
            className="flex items-center space-x-2 group"
          >
            <div className="relative">
              {/* Geometric logo placeholder */}
              <div className="w-8 h-8 bg-olive-green relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-olive-green to-mediterranean-blue" />
                <div className="absolute top-1 left-1 w-1 h-1 bg-stone-white opacity-80" />
                <div className="absolute bottom-1 right-1 w-1 h-1 bg-stone-white opacity-60 rounded-full" />
              </div>
            </div>
            <span className="font-mono text-xl font-bold text-pyrenean-slate group-hover:text-olive-green transition-colors">
              Camí
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-pyrenean-slate hover:text-olive-green transition-colors font-medium relative group"
              >
                {item.name}
                <div className="absolute bottom-0 left-0 w-0 h-0.5 bg-olive-green group-hover:w-full transition-all duration-300" />
              </Link>
            ))}
          </nav>

          {/* Language Switcher & Mobile Menu Button */}
          <div className="flex items-center space-x-4">
            <LanguageSwitcher className="hidden sm:flex" />
            
            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-pyrenean-slate hover:text-olive-green transition-colors"
              aria-label="Toggle menu"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-pyrenean-slate/10 py-4 grain-medium">
            <nav className="flex flex-col space-y-4">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-pyrenean-slate hover:text-olive-green transition-colors font-medium px-2 py-1"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
              <div className="pt-4 border-t border-pyrenean-slate/10">
                <LanguageSwitcher />
              </div>
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}
