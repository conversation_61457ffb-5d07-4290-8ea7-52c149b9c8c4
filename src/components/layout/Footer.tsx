'use client'

import Link from 'next/link'
import { useTranslations, useLocale } from 'next-intl'
import { Instagram, Mail, MessageCircle, Send } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { SOCIAL_LINKS } from '@/lib/constants'

export function Footer() {
  const t = useTranslations()
  const locale = useLocale()

  const navigation = [
    { name: t('navigation.home'), href: `/${locale}` },
    { name: t('navigation.tours'), href: `/${locale}/tours` },
    { name: t('navigation.about'), href: `/${locale}/about` },
    { name: t('navigation.gallery'), href: `/${locale}/gallery` },
    { name: t('navigation.contact'), href: `/${locale}/contact` },
  ]

  const socialLinks = [
    { name: 'Instagram', href: SOCIAL_LINKS.instagram, icon: Instagram },
    { name: 'WhatsApp', href: SOCIAL_LINKS.whatsapp, icon: MessageCircle },
    { name: 'Telegram', href: SOCIAL_LINKS.telegram, icon: Send },
    { name: 'Em<PERSON>', href: `mailto:${SOCIAL_LINKS.email}`, icon: Mail },
  ]

  return (
    <footer className="bg-pyrenean-slate text-stone-white grain-light">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-olive-green relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-br from-olive-green to-mediterranean-blue" />
                <div className="absolute top-1 left-1 w-1 h-1 bg-stone-white opacity-80" />
                <div className="absolute bottom-1 right-1 w-1 h-1 bg-stone-white opacity-60 rounded-full" />
              </div>
              <span className="font-mono text-xl font-bold">Camí</span>
            </div>
            <p className="text-stone-white/80 mb-6 max-w-md">
              {t('hero.subtitle')}
            </p>
            
            {/* Newsletter Signup */}
            <div className="mb-6">
              <h3 className="font-mono font-semibold mb-3">{t('footer.newsletter')}</h3>
              <div className="flex flex-col sm:flex-row gap-2 max-w-md">
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="flex-1 px-3 py-2 bg-stone-white/10 border border-stone-white/20 text-stone-white placeholder-stone-white/60 focus:outline-none focus:ring-2 focus:ring-olive-green font-mono text-sm"
                />
                <Button variant="accent" size="sm">
                  {t('footer.subscribe')}
                </Button>
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div>
            <h3 className="font-mono font-semibold mb-4">Navigation</h3>
            <nav className="space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block text-stone-white/80 hover:text-olive-green transition-colors"
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>

          {/* Contact & Social */}
          <div>
            <h3 className="font-mono font-semibold mb-4">Connect</h3>
            <div className="space-y-3">
              {socialLinks.map((link) => (
                <a
                  key={link.name}
                  href={link.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center space-x-2 text-stone-white/80 hover:text-olive-green transition-colors"
                >
                  <link.icon size={16} />
                  <span>{link.name}</span>
                </a>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-stone-white/20 mt-8 pt-8 flex flex-col sm:flex-row justify-between items-center">
          <p className="text-stone-white/60 text-sm">
            {t('footer.rights')}
          </p>
          
          {/* Geometric decorative elements */}
          <div className="flex items-center space-x-2 mt-4 sm:mt-0">
            <div className="w-2 h-2 bg-olive-green opacity-60" />
            <div className="w-2 h-2 bg-mediterranean-blue opacity-60 rounded-full" />
            <div className="w-2 h-2 bg-sun-baked-clay opacity-60 rotate-45" />
          </div>
        </div>
      </div>
    </footer>
  )
}
